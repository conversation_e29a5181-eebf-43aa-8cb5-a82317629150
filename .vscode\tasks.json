{"version": "2.0.0", "tasks": [{"label": "build", "type": "shell", "command": "C:/w64devkit/bin/gcc.exe", "args": ["-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"]}, {"type": "cppbuild", "label": "C/C++: gcc.exe 生成活动文件", "command": "C:/w64devkit/bin/gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe", ""], "options": {"cwd": "C:/w64devkit/bin"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}, {"type": "cppbuild", "label": "C/C++: g++.exe 生成活动文件", "command": "C:\\w64devkit\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}]}