#include<stdio.h>
#include<stdlib.h>
#define ERROR 0
#define SUCCESS 1
struct LNode{
    char data;
    struct LNode *next;
};
typedef struct LNode LNode;
typedef struct LNode * LinkList;
int Creat_L(LinkList *L){
    char ch;
    LinkList p, r;
    *L = (LinkList)malloc(sizeof(LNode));
    if(*L == NULL){
        printf("内存分配失败!\n");
        exit(ERROR);
    }
    r = *L;
    r->next = NULL;
    printf("请输入链表结点的字符，以 '#' 结束:\n");
    while((ch = getchar()) != '#' && ch != '\n'){
        p = (LinkList)malloc(sizeof(LNode));
        if(p == NULL){
            printf("内存分配失败!\n");
            exit(ERROR);
        }
        p->data = ch;
        p->next = NULL;
        r->next = p;
        r = p;
    }
    return SUCCESS;
}
void Print_L(LinkList L){
    LinkList p = L->next;
    while(p){
        printf("%c ", p->data);
        p = p->next;
    }
    printf("\n");
}
int ListDelete_L(LinkList *L, int i, char *e){
    LinkList p, q;
    int j;
    if((*L)->next == NULL){
        printf("链表为空, 无法删除!\n");
        return ERROR;
    }
    if(i < 1)
        return ERROR;
    p = (*L);
    j = 0;
    while(p->next != NULL && j < i-1){
        p = p->next;
        j++;
    }
    if(p->next == NULL)
        return ERROR;
    q = p->next;
    p->next = q->next;
    (*e) = q->data;
    free(q);
    return SUCCESS;
}
int main(){
    LinkList L;
    int k;
    char ch;
    k = Creat_L(&L);
    Print_L(L);
    k = ListDelete_L(&L, 1, &ch);
    if(k == SUCCESS){
        printf("删除成功\n");
        printf("删除的字符为: %c\n", ch);
        Print_L(L);
    } else {
        printf("删除失败\n");
    }
    return 0;
}
