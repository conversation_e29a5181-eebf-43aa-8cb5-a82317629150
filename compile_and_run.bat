@echo off
echo 正在尝试使用 Visual Studio 编译器...

REM 尝试找到 Visual Studio 安装路径
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto compile
)

if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto compile
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto compile
)

echo 未找到 Visual Studio 编译器
echo 请安装 MinGW-w64 或 Visual Studio Community
pause
exit /b 1

:compile
echo 编译中...
cl /EHsc "数据结构cpp.cpp" /Fe:"数据结构cpp.exe"
if %errorlevel% equ 0 (
    echo 编译成功！正在运行程序...
    echo.
    "数据结构cpp.exe"
) else (
    echo 编译失败！
)
pause
