{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Desktop/新建文件夹 (2)", "program": "${fileDirname}/${fileBasenameNoExtension}.exe", "MIMode": "gdb", "miDebuggerPath": "C:/w64devkit/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}, {"name": "C/C++: gcc.exe 构建和调试活动文件", "type": "cppdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "C:/w64devkit/bin", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:\\w64devkit\\bin\\gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "C/C++: gcc.exe 生成活动文件"}]}